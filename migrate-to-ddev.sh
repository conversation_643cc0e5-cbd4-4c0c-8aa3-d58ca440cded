#!/bin/bash

set -e

# Colors for output
COLOR_RED="$(tput setaf 1; tput bold)"
COLOR_GREEN="$(tput setaf 2; tput bold)"
COLOR_YELLOW="$(tput setaf 3; tput bold)"
COLOR_BLUE="$(tput setaf 4; tput bold)"
COLOR_RESET="$(tput sgr0)"

function __attn() { printf "${COLOR_GREEN}$*${COLOR_RESET}\n"; }
function __info() { printf "${COLOR_YELLOW}$*${COLOR_RESET}\n"; }
function __err() { printf "${COLOR_RED}$*${COLOR_RESET}\n";}

__attn "=== CopeX Tools to ddev Migration Script ==="
echo

__info "This script will migrate your current CopeX Tools setup to ddev."
__info "Your existing setup will be preserved as backup."
echo

# Check if we're in a Magento project directory
if [ ! -f "composer.json" ] && [ ! -d "app" ]; then
    __err "This doesn't appear to be a Magento project directory."
    __err "Please run this script from your Magento project root."
    exit 1
fi

# Check if ddev is installed
if ! command -v ddev &> /dev/null; then
    __err "ddev is not installed. Please install ddev first:"
    __err "curl -fsSL https://raw.githubusercontent.com/ddev/ddev/master/scripts/install_ddev.sh | bash"
    exit 1
fi

# Check if there's an existing docker-compose setup
if [ -f "docker-compose.yml" ] || [ -f "config/docker-compose.yml" ]; then
    __info "Found existing docker-compose setup."
    
    # Check if containers are running
    if docker-compose ps -q 2>/dev/null | grep -q .; then
        __info "Stopping existing containers..."
        docker-compose down
    fi
    
    # Backup existing database if MySQL container exists
    if docker-compose ps -a | grep -q mysql; then
        __info "Backing up existing database..."
        mkdir -p .migration-backup
        docker-compose up -d mysql
        sleep 5
        docker-compose exec -T mysql mysqldump -u magento -pmagento magento > .migration-backup/database-backup.sql 2>/dev/null || \
        docker-compose exec -T mysql mysqldump -u root -pr00t magento > .migration-backup/database-backup.sql 2>/dev/null || \
        __info "Could not backup database automatically. You may need to do this manually."
        docker-compose down
    fi
fi

# Copy ddev configuration from magento-tools
TOOLS_DIR="$(dirname "$(readlink -f "$0")")"
if [ -d "$TOOLS_DIR/.ddev" ]; then
    __info "Copying ddev configuration..."
    cp -r "$TOOLS_DIR/.ddev" .
    
    # Update project name in config
    if [ -f ".ddev/config.yaml" ]; then
        PROJECT_NAME=$(basename "$(pwd)")
        sed -i "s/name: magento-tools/name: $PROJECT_NAME/" .ddev/config.yaml
    fi
else
    __err "ddev configuration not found in magento-tools directory."
    exit 1
fi

# Detect Magento version and update config
if [ -f "bin/magento" ]; then
    __info "Detected Magento 2"
    sed -i 's/type: magento2/type: magento2/' .ddev/config.yaml
elif [ -f "app/Mage.php" ]; then
    __info "Detected Magento 1"
    sed -i 's/type: magento2/type: php/' .ddev/config.yaml
    sed -i 's/docroot: ""/docroot: ""/' .ddev/config.yaml
fi

# Start ddev
__info "Starting ddev environment..."
ddev start

# Import database if backup exists
if [ -f ".migration-backup/database-backup.sql" ]; then
    __info "Importing database backup..."
    ddev import-db --src=.migration-backup/database-backup.sql
fi

# Create composer cache volume if it doesn't exist
if ! docker volume ls | grep -q ddev-composer-cache; then
    __info "Creating composer cache volume..."
    docker volume create ddev-composer-cache
fi

# Update symlinks
__info "Updating command symlinks..."
cd /usr/local/bin
sudo rm -f c copex magento m 2>/dev/null || true

# Create new symlink to ddev command
sudo ln -sf "$TOOLS_DIR/.ddev/commands/host/c" c
sudo ln -sf c copex  
sudo ln -sf c magento
sudo ln -sf c m

__attn "=== Migration Complete! ==="
echo
__info "Your CopeX Tools have been successfully migrated to ddev!"
__info "All your familiar commands still work:"
__info "  c start    - Start the environment"
__info "  c stop     - Stop the environment"  
__info "  c composer - Run composer commands"
__info "  c enter    - Enter the web container"
echo
__info "New ddev-specific commands are also available:"
__info "  ddev start, ddev stop, ddev ssh, etc."
echo
__info "Your project is now running at: https://$(ddev describe | grep -o '[^/]*\.ddev\.site' | head -1)"
echo
__attn "Happy coding! 🚀"
