version: '3.6'

services:
  # Additional services to match your original setup
  web:
    environment:
      - DOMAIN=magento.ddev.site
      - MAGENTO_ROOT=/var/www/html
      - MAGENTO_DEVELOPERMODE=1
    volumes:
      # Mount composer cache for better performance
      - composer-cache:/var/www/.composer
  
  # Cron service (similar to your original copex/cron)
  cron:
    image: ddev/ddev-utilities:latest
    container_name: ddev-${DDEV_SITENAME}-cron
    volumes:
      - ".:/var/www/html"
      - composer-cache:/var/www/.composer
    environment:
      - DDEV_SITENAME=${DDEV_SITENAME}
    command: >
      bash -c "
        while true; do
          echo 'Cron service running...'
          sleep 3600
        done
      "
    labels:
      com.ddev.site-name: ${DDEV_SITENAME}
      com.ddev.approot: $DDEV_APPROOT

volumes:
  composer-cache:
    external: true
    name: ddev-composer-cache
