#!/bin/bash

## Description: Connect to Redis CLI
## Usage: redis-cli [redis-service] [commands]
## Example: "ddev redis-cli" or "ddev redis-cli redis-session"

service=${1:-redis}
shift || true

case "$service" in
    session|redis-session)
        redis-cli -h redis-session $@
        ;;
    fpc|fullpage|redis-fpc)
        redis-cli -h redis-fpc $@
        ;;
    cache|redis|*)
        redis-cli -h redis $@
        ;;
esac
