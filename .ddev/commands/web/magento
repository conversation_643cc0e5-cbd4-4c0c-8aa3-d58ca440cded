#!/bin/bash

## Description: Magento CLI commands
## Usage: magento [command]
## Example: "ddev magento cache:flush" or "ddev magento setup:upgrade"

# Check if we're in Magento 1 or 2
if [ -f "/var/www/html/bin/magento" ]; then
    # Magento 2
    php /var/www/html/bin/magento $@
elif [ -f "/var/www/html/shell/indexer.php" ]; then
    # Magento 1 - handle common commands
    case "$1" in
        cache:flush|cache:clean)
            echo "Clearing Magento 1 cache..."
            rm -rf /var/www/html/var/cache/*
            ;;
        reindex)
            php /var/www/html/shell/indexer.php --reindexall
            ;;
        *)
            echo "Magento 1 detected. Available commands:"
            echo "  cache:flush - Clear cache"
            echo "  reindex     - Reindex all"
            ;;
    esac
else
    echo "Magento installation not found or not properly configured."
fi
