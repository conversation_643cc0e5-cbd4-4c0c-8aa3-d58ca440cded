#!/bin/bash

## Description: CopeX Tools compatibility layer for ddev
## Usage: c [command] [parameters]
## Example: "c start" or "c composer install"

set -e

# Colors for output (matching your original script)
COLOR_RED="$(tput setaf 1; tput bold)"
COLOR_GREEN="$(tput setaf 2; tput bold)"
COLOR_YELLOW="$(tput setaf 3; tput bold)"
COLOR_BLUE="$(tput setaf 4; tput bold)"
COLOR_MAGENTA="$(tput setaf 5; tput bold)"
COLOR_CYAN="$(tput setaf 6; tput bold)"
COLOR_WHITE="$(tput setaf 7; tput bold)"
COLOR_RESET="$(tput sgr0)"

function __attn() { printf "${COLOR_GREEN}$*${COLOR_RESET}\n"; }
function __msg() { printf "$*\n"; }
function __info() { printf "${COLOR_YELLOW}$*${COLOR_RESET}\n"; }
function __err() { printf "${COLOR_RED}$*${COLOR_RESET}\n";}
function __help() { printf "  ${COLOR_GREEN}c %-30s${COLOR_RESET}\t${COLOR_YELLOW}%s${COLOR_RESET}\n" "${1}" "${2}"; }
function __mainhead() { printf "\n${COLOR_WHITE}$*${COLOR_RESET}\n"; }
function __head() { printf "\n${COLOR_CYAN}$*${COLOR_RESET}\n"; }

function copex_help() {
    __mainhead "CopeX Tools commands (ddev powered)"
    __help "start" "Starts the ddev environment"
    __help "stop" "Stops the ddev environment"
    __help "restart" "Restarts the ddev environment"
    __help "status" "Shows status of ddev environment"
    __help "destroy" "Stops and removes the ddev environment"
    __help "logs" "Show logs of all containers"
    __help "enter" "Enters the web container bash"
    __help "composer" "Executes composer in the web container"
    __help "stats" "Displays live resource usage statistics"
    __head "cache"
    __help "cache:clean:redis" "Clean all redis caches"
    __head "docker"
    __help "docker:remove:old" "Remove old docker containers"
    __help "docker:stats" "Show docker container statistics"
    __head "utilities"
    __help "whatsmyip" "Show your external IP address"
    __help "selfupdate" "Update ddev to latest version"
}

# Get the action and parameters
action=$1
shift || true
parameters="$@"

case "${action}" in
    start)
        __info "Starting ddev environment..."
        ddev start
        ;;
    
    stop)
        __info "Stopping ddev environment..."
        ddev stop
        ;;
    
    restart)
        __info "Restarting ddev environment..."
        ddev restart
        ;;
    
    status)
        ddev status
        ;;
    
    destroy)
        echo "This will stop and remove the ddev environment."
        read -p "Do you want to remove the database? (y/n): " yn
        case $yn in
            [Yy]* ) 
                __info "Removing ddev environment with database..."
                ddev delete -y
                ;;
            * ) 
                __info "Stopping ddev environment (keeping database)..."
                ddev stop
                ;;
        esac
        ;;
    
    logs)
        ddev logs -f $parameters
        ;;
    
    enter)
        if [ "$parameters" ]; then
            # If container type specified, try to enter that service
            case "$parameters" in
                mysql|db)
                    ddev mysql
                    ;;
                redis)
                    ddev redis-cli
                    ;;
                *)
                    ddev ssh
                    ;;
            esac
        else
            ddev ssh
        fi
        ;;
    
    composer)
        ddev composer $parameters
        ;;
    
    stats)
        ddev exec docker stats --no-stream
        ;;
    
    cache:clean:redis)
        __info "Cleaning Redis caches..."
        ddev redis-cli -h redis flushall
        ddev redis-cli -h redis-session flushall  
        ddev redis-cli -h redis-fpc flushall
        __attn "All Redis caches cleared!"
        ;;
    
    docker:remove:old)
        __info "Removing old docker containers..."
        docker container prune -f
        docker image prune -f
        docker volume prune -f
        ;;
    
    docker:stats)
        docker stats --no-stream
        ;;
    
    whatsmyip)
        curl -s http://whatismyip.akamai.com/
        echo
        ;;
    
    selfupdate)
        __info "Updating ddev..."
        curl -fsSL https://raw.githubusercontent.com/ddev/ddev/master/scripts/install_ddev.sh | bash
        ;;
    
    install)
        __info "Starting ddev and running installation..."
        ddev start
        if [ -f "config/install.sh" ]; then
            __info "Running custom install script..."
            ddev exec bash /var/www/html/config/install.sh
        fi
        ;;
    
    install:custom)
        if [ -f "config/install.sh" ]; then
            __info "Running custom install script..."
            ddev exec bash /var/www/html/config/install.sh
        else
            __err "No custom install script found at config/install.sh"
        fi
        ;;
    
    run)
        ddev exec $parameters
        ;;
    
    runroot)
        ddev exec --user root $parameters
        ;;
    
    sa|stop:all)
        __info "Stopping all ddev projects..."
        ddev poweroff
        ;;
    
    ss|stop:start|startit)
        __info "Stopping all ddev projects and starting this one..."
        ddev poweroff
        ddev start
        ;;
    
    *)
        copex_help
        ;;
esac
