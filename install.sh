#!/bin/bash

workingDir=$(pwd)
scriptPath="${workingDir}/c.sh"
ddevScriptPath="${workingDir}/.ddev/commands/host/c"

cd /usr/local/bin

rm -f c copex magento m

# Check if ddev version is available and use it, otherwise fall back to original
if [ -f "$ddevScriptPath" ] && command -v ddev &> /dev/null; then
    echo "Installing ddev-powered CopeX Tools..."
    sudo ln -s ${ddevScriptPath} c
else
    echo "Installing traditional CopeX Tools..."
    sudo ln -s ${scriptPath} c
fi

sudo ln -s c copex
sudo ln -s c magento
sudo ln -s c m

# check if group already exists. Otherwise create it
if [ ! $(getent group docker) ]; then
  echo "Adding current user to docker group..."
  sudo groupadd docker
fi
if ! id -nG "$USER" | grep -qw "docker"; then
  sudo usermod -aG docker $USER
  echo "Done. Restart required to apply group changes!"
fi

