# Cope<PERSON> Tools → ddev Migration

Diese Anleitung hilft Ihnen dabei, Ihre bestehende CopeX Tools Umgebung nahtlos auf ddev zu migrieren.

## 🚀 Schnellstart

### 1. Migration durchführen

Gehen Sie in Ihr Magento-Projektverzeichnis und führen Sie aus:

```bash
/pfad/zu/magento-tools/migrate-to-ddev.sh
```

Das war's! Alle Ihre gewohnten Befehle funktionieren weiterhin.

### 2. Gewohnte Befehle verwenden

```bash
c start          # Startet die Umgebung
c stop           # Stoppt die Umgebung  
c composer install  # Führt Composer aus
c enter          # Betritt den Container
c logs           # Zeigt Logs an
```

## 📋 Befehlsübersicht

### Basis-Befehle (wie gewohnt)
| Alter <PERSON>hl | Neuer Befehl | Beschreibung |
|--------------|--------------|--------------|
| `c start` | `c start` | Startet die Umgebung |
| `c stop` | `c stop` | Stoppt die Umgebung |
| `c restart` | `c restart` | Neustart |
| `c status` | `c status` | Status anzeigen |
| `c destroy` | `c destroy` | Umgebung löschen |
| `c enter` | `c enter` | Container betreten |
| `c composer` | `c composer` | Composer ausführen |
| `c logs` | `c logs` | Logs anzeigen |

### Cache-Befehle
| Befehl | Beschreibung |
|--------|--------------|
| `c cache:clean:redis` | Alle Redis-Caches leeren |

### Erweiterte Befehle
| Befehl | Beschreibung |
|--------|--------------|
| `c stats` | Container-Statistiken |
| `c whatsmyip` | Externe IP anzeigen |
| `c selfupdate` | ddev aktualisieren |

### Zusätzliche ddev-Befehle
| Befehl | Beschreibung |
|--------|--------------|
| `ddev ssh` | Container betreten |
| `ddev mysql` | MySQL-CLI öffnen |
| `ddev redis-cli` | Redis-CLI öffnen |
| `ddev magento` | Magento-CLI ausführen |

## 🔧 Was ändert sich?

### Vorteile der Migration:
- ✅ **Bessere Performance** - ddev ist optimiert für Entwicklung
- ✅ **Automatische SSL-Zertifikate** - HTTPS funktioniert sofort
- ✅ **Bessere Portabilität** - Funktioniert auf allen Systemen gleich
- ✅ **Integrierte Tools** - MailHog, phpMyAdmin, etc. inklusive
- ✅ **Einfachere Wartung** - Weniger Konfiguration nötig

### Was bleibt gleich:
- ✅ **Alle Ihre Befehle** - `c start`, `c composer`, etc.
- ✅ **Ihre Datenbank** - Wird automatisch migriert
- ✅ **Ihre Dateien** - Bleiben unverändert
- ✅ **Ihr Workflow** - Arbeiten Sie wie gewohnt

## 🌐 URLs nach der Migration

Nach der Migration ist Ihr Projekt erreichbar unter:
- **HTTP**: `http://projektname.ddev.site`
- **HTTPS**: `https://projektname.ddev.site`
- **MailHog**: `http://projektname.ddev.site:8025`
- **phpMyAdmin**: `http://projektname.ddev.site:8036`

## 🔍 Troubleshooting

### Port-Konflikte
Falls Port 80/443 bereits belegt sind:
```bash
ddev config --router-http-port=8080 --router-https-port=8443
ddev restart
```

### Datenbank-Import-Probleme
Falls die automatische Datenbank-Migration fehlschlägt:
```bash
# Manueller Import
ddev import-db --src=pfad/zur/datenbank.sql
```

### Performance-Optimierung
Für bessere Performance auf macOS/Windows:
```bash
ddev config --mutagen-enabled=true
ddev restart
```

## 📚 Weitere Informationen

- [ddev Dokumentation](https://ddev.readthedocs.io/)
- [Magento 2 mit ddev](https://ddev.readthedocs.io/en/stable/users/cli-usage/#magento-2)

## 🆘 Support

Bei Problemen:
1. Prüfen Sie `ddev logs`
2. Versuchen Sie `ddev restart`
3. Bei persistenten Problemen: `ddev delete` und neu starten

---

**Hinweis**: Ihre alte docker-compose.yml wird als Backup aufbewahrt. Sie können jederzeit zurückwechseln, falls nötig.
